!R::
{
    ; 获取当前活动窗口的工作目录
    CurrentDir := ""

    ; 检查当前活动窗口是否是资源管理器
    if WinActive("ahk_class CabinetWClass") or WinActive("ahk_class ExploreWClass") {
        ; 获取资源管理器当前路径
        try {
            shell := ComObject("Shell.Application")
            windows := shell.Windows
            activeHwnd := WinGetID("A")

            for window in windows {
                try {
                    if window.HWND = activeHwnd {
                        CurrentDir := window.Document.Folder.Self.Path
                        break
                    }
                }
            }
        }
    }

    ; 如果没有获取到目录，使用默认方法
    if (CurrentDir = "") {
        CurrentDir := A_WorkingDir
    }

    ; 启动WindowsTerminal并在指定目录运行crush命令
    ; 使用--startingDirectory参数直接在指定目录启动
    ProcessID := Run("C:\tools\WindowsTerminal\WindowsTerminal.exe --startingDirectory `"" . CurrentDir . "`" cmd /k crush")
    
    ; 尝试多种窗口标识符等待窗口出现
    WindowFound := False
    Loop 50 {  ; 最多等待5秒
        ; 尝试不同的窗口标识符
        if WinExist("ahk_pid " . ProcessID) {
            WindowID := "ahk_pid " . ProcessID
            WindowFound := True
            break
        }
        else if WinExist("ahk_exe WindowsTerminal.exe") {
            WindowID := "ahk_exe WindowsTerminal.exe"
            WindowFound := True
            break
        }
        else if WinExist("ahk_class CASCADIA_HOSTING_WINDOW_CLASS") {
            WindowID := "ahk_class CASCADIA_HOSTING_WINDOW_CLASS"
            WindowFound := True
            break
        }
        else if WinExist("Windows PowerShell") {
            WindowID := "Windows PowerShell"
            WindowFound := True
            break
        }
        Sleep 100
    }
    
    if (!WindowFound) {
        MsgBox("WindowsTerminal 窗口未找到，请检查路径和进程")
        return
    }

    ; 等待窗口完全加载
    Sleep 500

    ; 再次确认窗口存在
    if (!WinExist(WindowID)) {
        MsgBox("窗口在操作前消失了")
        return
    }

    ; 简单的窗口最大化操作
    try {
        WinMaximize WindowID
        WinActivate WindowID
    } catch Error as e {
        MsgBox("最大化失败: " . e.Message)
    }
}
